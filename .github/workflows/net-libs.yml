name: Libraries (.NET 9.0)

on:
  push:
    branches:
      - master
      - dev
    paths:
      - libs/net/**
  pull_request:
    branches:
      - master
      - dev
    paths:
      - libs/net/**

jobs:
  build-net-libs:
    runs-on: ubuntu-latest
    env:
      working-directory: ./libs/net
      codeCov-token: ${{ secrets.CodeCov }}

    steps:
      - uses: actions/checkout@v2

      - name: Extract Branch Name
        shell: bash
        run: echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
        id: extract_branch

      - name: Setup .NET 9
        uses: actions/setup-dotnet@v4.1.0
        with:
          dotnet-version: 9.x

      - name: Install dependencies
        run: dotnet restore
        working-directory: ${{env.working-directory}}

      - name: Install coverlet for code coverage
        run: dotnet tool install -g coverlet.console --version 1.7.2
        working-directory: ${{env.working-directory}}

      - name: Build
        run: dotnet build --configuration Release --no-restore
        working-directory: ${{env.working-directory}}

      - name: Test
        run: dotnet test --no-restore --verbosity normal
        working-directory: ${{env.working-directory}}

        # For future reference, if we have N test projects the flow of events would be:
        #
        # **Pre-conditions:**
        # - All projects export their individual coverage percents in JSON and OpenCover format
        # - There's no way to merge OpenCover xmls together (that I could find)
        # - Common folder "../TestResults" is  git ignored so nothing gets in source control
        #
        # **Steps:**
        #
        # - Test-project 1
        #   - generate coverage files (without merging)
        #   - copy results to common folder "../TestResults"
        # - Test-project 2
        #   - generate coverage files merging with previous `coverage.json`
        #   - the previous `coverage.opencoverage.xml` is ignored
        #   - copy results to common folder "../TestResults"
        # ...
        # - Test-project N
        #   - generate coverage files merging with previous `coverage.json`
        #   - the previous `coverage.opencoverage.xml` is ignored
        #   - copy results to common folder "../TestResults"
        #
        # The final `coverage.opencover.xml` is the one we want
      - name: Generate code coverage
        working-directory: ${{env.working-directory}}/tests
        run: |
          mkdir -p TestResults
          rm -rf dal/TestResults
          rm -rf entities/TestResults
          rm -rf services/TestResults

          cd dal
          dotnet test --collect:"XPlat Code Coverage" --settings coverlet.runsettings --no-restore
          mv TestResults/*/* ../TestResults/
          cd ..

          cd entities
          dotnet test --collect:"XPlat Code Coverage" --settings coverlet.runsettings --no-restore
          mv TestResults/*/* ../TestResults/
          cd ..

          cd services
          dotnet test --collect:"XPlat Code Coverage" --settings coverlet.runsettings --no-restore
          mv TestResults/*/* ../TestResults/
          cd ..

          head TestResults/coverage.opencover.xml

      - name: Codecov
        uses: codecov/codecov-action@v1.5.0
        if: ${{ false }}
        with:
          # User defined upload name. Visible in Codecov UI
          name: TNO
          # Repository upload token - get it from codecov.io. Required only for private repositories
          token: ${{env.codeCov-token}}
          # Path to coverage file to upload
          file: ${{env.working-directory}}/tests/TestResults/coverage.opencover.xml
          # Flag upload to group coverage metrics (e.g. unittests | integration | ui,chrome)
          flags: unittests
          # Environment variables to tag the upload with (e.g. PYTHON | OS,PYTHON)
          env_vars: C#
          # Specify whether or not CI build should fail if Codecov runs into an error during upload
          fail_ci_if_error: true

      # - name: SonarScanner for .NET 9 with pull request decoration support
      #   id: scan
      #   uses: highbyte/sonarscan-dotnet@2.0
      #   if: ${{ false }}
      #   # if: github.event_name == 'push'
      #   env:
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      #   with:
      #     dotnetBuildArguments: ${{env.working-directory}}
      #     dotnetTestArguments: ${{env.working-directory}} --logger trx --collect:"XPlat Code Coverage" -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Format=opencover
      #     # Optional extra command arguments the the SonarScanner 'begin' command
      #     sonarBeginArguments: /d:sonar.cs.opencover.reportsPaths="**/TestResults/**/coverage.opencover.xml" -d:sonar.cs.vstest.reportsPaths="**/TestResults/*.trx" -d:sonar.qualitygate.wait=true
      #     # The key of the SonarQube project
      #     sonarProjectKey: ${{secrets.PROJECT_KEY_API}}
      #     # The name of the SonarQube project
      #     sonarProjectName: TNO-API
      #     # The SonarQube server URL. For SonarCloud, skip this setting.
      #     sonarHostname: ${{secrets.SONAR_URL}}

      # - name: Find Comment
      #   if: ${{ false }}
      #   # if: failure() && steps.scan.outcome == 'failure' && github.event_name == 'pull_request' && github.event.pull_request.head.repo.full_name == github.repository
      #   uses: peter-evans/find-comment@v1
      #   id: fc
      #   with:
      #     issue-number: ${{ github.event.pull_request.number }}
      #     comment-author: "github-actions[bot]"
      #     body-includes: QUALITY GATE STATUS FOR .NET 9

      # - name: Check Quality Gate and Create Comment
      #   if: ${{ false }}
      #   # if: failure() && steps.scan.outcome == 'failure' && github.event_name == 'pull_request' && steps.fc.outputs.comment-id == ''
      #   uses: peter-evans/create-or-update-comment@v1
      #   env:
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      #     SONAR_HOST_URL: ${{ secrets.SONAR_URL }}
      #     PROJECT_KEY: ${{secrets.PROJECT_KEY_API}}
      #   with:
      #     issue-number: ${{ github.event.pull_request.number }}
      #     body: |
      #       QUALITY GATE STATUS FOR .NET 9: FAILED.

      #       [View and resolve details on][1]

      #       [1]: ${{env.SONAR_HOST_URL}}/dashboard?id=${{env.PROJECT_KEY}}
      #     reactions: confused

      # - name: Check Quality Gate and Update Comment
      #   if: ${{ false }}
      #   # if: failure() && steps.scan.outcome == 'failure' && github.event_name == 'pull_request' && steps.fc.outputs.comment-id != '' && github.event.pull_request.head.repo.full_name == github.repository
      #   uses: peter-evans/create-or-update-comment@v1
      #   env:
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      #     SONAR_HOST_URL: ${{ secrets.SONAR_URL }}
      #     PROJECT_KEY: ${{secrets.PROJECT_KEY_APP}}
      #   with:
      #     comment-id: ${{ steps.fc.outputs.comment-id }}
      #     issue-number: ${{ github.event.pull_request.number }}
      #     body: |
      #       QUALITY GATE STATUS FOR .NET 9: FAILED.

      #       [View and resolve details on][1]

      #       [1]: ${{env.SONAR_HOST_URL}}/dashboard?id=${{env.PROJECT_KEY}}
      #     edit-mode: replace
      #     reactions: eyes
