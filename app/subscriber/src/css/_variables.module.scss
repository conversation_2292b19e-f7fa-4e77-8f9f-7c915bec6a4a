@import './_variables';

:export {
  // The purpose of this file is to help us create a theme so that when UI colour changes we can control them in one place.
  // Shadows
  dropShadow: $drop-shadow;
  boxShadow: $box-shadow;

  // Backgrounds
  bkPrimary: $bk-primary;
  bkPrimary25: $bk-primary-25;
  bkPrimary75: $bk-primary-75;
  bkSecondary: $bk-secondary;
  bkTertiary: $bk-tertiary;
  bkWhite: $bk-white;
  bkStaticGray: $bk-static-gray;
  bkQuaternary: $bk-quaternary;
  bkError: $bk-error;
  bkForm: $bk-form;
  bkMain: $bk-main;
  teaserBackground: $teaser-background;
  stickyNoteColor: $sticky-note-color;
  bkInfo: $bkInfo;

  productionBackgroundColor: $production-background-color;
  developmentBackgroundColor: $development-background-color;
  testBackgroundColor: $test-background-color;

  // Highlights
  highlightPrimary: $highlightPrimary;
  highlightSecondary: $highlightSecondary;
  highlightTertiary: $highlightTertiary;
  highlightActive: $highlight-active;
  highlightYellow: $highlight-yellow;
  highlightYellowDarker: $highlight-yellow-darker;

  // Report Types
  reportAuto: $report-auto;
  reportFullAuto: $report-full-auto;
  reportManual: $report-manual;

  // Fonts
  fPrimary: $f-primary;
  fSecondary: $f-secondary;
  fSemiBold: $f-semi-bold;
  fPrimaryColor: $f-primary-color;
  fPrimaryInvertColor: $f-primary-invert-color;
  hPrimaryColor: $h-primary-color;
  fRedColor: $f-red-color;
  fSecondaryColor: $f-secondary-color;
  fInfo: $fInfo;

  // Dialog boxes
  dialogBoxBkPrimary: $dialog-box-bk-primary;
  dialogBoxBkSecondary: $dialog-box-bk-secondary;

  // Tone related
  tonePositive: $tone-positive;
  toneNegative: $tone-negative;
  toneNeutral: $tone-neutral;

  // Links
  linkPrimaryColor: $link-primary-color;
  linkPrimaryHoverColor: $link-primary-hover-color;
  linkPrimaryActiveColor: $link-primary-active-color;
  linkGrayColor: $link-gray-color;
  linkVisited: $link-visited;

  // Icons
  iconPrimaryColor: $icon-primary-color;
  iconSecondaryColor: $icon-secondary-color;
  iconGrayColor: $icon-gray-color;
  iconTertiaryColor: $icon-tertiary-color;
  iconDarkColor: $icon-dark-color;
  iconPurpleColor: $icon-purple-color;
  sideBarIconColor: $sidebar-icon-color;
  sideBarIconHoverColor: $sidebar-icon-hover-color;
  iconGreen: $icon-green;
  overviewTranscript: $overview-transcript;
  overviewVideo: $overview-video;

  // Buttons
  btnBkPrimary: $btn-bk-primary;
  btnPrimaryColor: $btn-primary-color;
  btnBkSecondary: $btn-bk-secondary;
  btnSecondaryColor: $btn-secondary-color;
  btnGrayColor: $btn-gray-color;
  btnRedColor: $btn-red-color;
  btnLightRedColor: $btn-light-red-color;
  btnYellowColor: $btn-yellow-color;
  btnYellowBkColor: $btn-yellow-bk-color;
  btnBkDisabled: $btn-bk-disabled;
  btnDisabledColor: $btn-disabled-color;
  btnBkSuccess: $btn-bk-success;
  btnSuccessColor: $btn-success-color;
  btnBkWarn: $btn-bk-warn;
  btnWarnColor: $btn-warn-color;
  btnBkError: $btn-bk-error;
  btnErrorColor: $btn-error-color;
  btnEditColor: $btn-edit-color;
  btnEditBorderColor: $btn-edit-border-color;
  btnEditHoverColor: $btn-edit-hover-color;

  inputGrey: $input-grey;
  iconButtonBorderColor: $icon-button-border-color;

  // Lines
  linePrimaryColor: $line-primary-color;
  lineSecondaryColor: $line-secondary-color;
  lineTertiaryColor: $line-tertiary-color;
  lineQuaternaryColor: $line-quaternary-color;
  lineErrorColor: $line-error-color;

  // Code blocks
  codeBlockColor: $code-block-color;
  teaserBackgroundColor: $teaser-background-color;
  tagBackgroundColor: $tag-background-color;

  // Navbar
  navItemBackgroundColor: $nav-item-background-color;
  navItemSecondaryBackgroundColor: $nav-item-secondary-background-color;

  // Tables
  tableOddRow: $table-odd-row;
  tableEvenRow: $table-even-row;
  tableActiveRow: $table-active-row;
  tableActiveRowColor: $table-active-row-color;
  tableHoverRow: $table-hover-row;

  // Sections
  sectionHeader: $section-header;
  sectionHeaderText: $section-header-text;
  sectionGroupHeader: $section-group-header;

  // Header variants
  darkHeaderColor: $dark-header-color;

  // Forms
  inputRequiredBorderColor: $inputRequiredBorderColor;
}
