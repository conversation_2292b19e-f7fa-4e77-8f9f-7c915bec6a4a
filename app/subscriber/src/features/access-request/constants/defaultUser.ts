import { IUserModel, UserAccountTypeName, UserStatusName } from 'tno-core';

export const defaultUser: IUserModel = {
  id: 0,
  key: '********-0000-0000-0000-************',
  username: '',
  email: '',
  preferredEmail: '',
  displayName: '',
  firstName: '',
  lastName: '',
  isEnabled: true,
  status: UserStatusName.Activated,
  emailVerified: false,
  isSystemAccount: false,
  accountType: UserAccountTypeName.Direct,
  note: '',
  roles: [],
  uniqueLogins: 0,
};
