export const continuousSchedule =
  'A continuous schedule is for services that are required to run every X minutes.';

export const singleSchedule =
  'A start & stop service starts at the schedule time and continues to run until the stop time.  This schedule is most commonly used by audio/video capture services.';

export const advancedSchedule =
  'An advanced schedule provides a way to manage when a service starts and stops throughout the day. Enabling multiple start & stop events to capture snippets from audio and video sources.';
