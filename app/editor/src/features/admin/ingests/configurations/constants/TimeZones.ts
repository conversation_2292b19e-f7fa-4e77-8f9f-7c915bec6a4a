import { OptionItem } from 'tno-core';

export const TimeZones = [
  new OptionItem('None', undefined),
  new OptionItem('(UTC-12:00) International Date Line West', 'Dateline Standard Time'),
  new OptionItem('(UTC-11:00) Coordinated Universal Time-11', 'UTC-11'),
  new OptionItem('(UTC-10:00) Aleutian Islands', 'Aleutian Standard Time'),
  new OptionItem('(UTC-10:00) Hawaii', 'Hawaiian Standard Time'),
  new OptionItem('(UTC-09:30) Marquesas Islands', 'Marquesas Standard Time'),
  new OptionItem('(UTC-09:00) Alaska', 'Alaskan Standard Time'),
  new OptionItem('(UTC-09:00) Coordinated Universal Time-09', 'UTC-09'),
  new OptionItem('(UTC-08:00) Baja California', 'Pacific Standard Time (Mexico)'),
  new OptionItem('(UTC-08:00) Coordinated Universal Time-08', 'UTC-08'),
  new OptionItem('(UTC-08:00) Pacific Time (US & Canada)', 'Pacific Standard Time'),
  new OptionItem('(UTC-07:00) Arizona', 'US Mountain Standard Time'),
  new OptionItem('(UTC-07:00) Chihuahua, La Paz, Mazatlan', 'Mountain Standard Time (Mexico)'),
  new OptionItem('(UTC-07:00) Mountain Time (US & Canada)', 'Mountain Standard Time'),
  new OptionItem('(UTC-06:00) Central America', 'Central America Standard Time'),
  new OptionItem('(UTC-06:00) Central Time (US & Canada)', 'Central Standard Time'),
  new OptionItem('(UTC-06:00) Easter Island', 'Easter Island Standard Time'),
  new OptionItem(
    '(UTC-06:00) Guadalajara, Mexico City, Monterrey',
    'Central Standard Time (Mexico)',
  ),
  new OptionItem('(UTC-06:00) Saskatchewan', 'Canada Central Standard Time'),
  new OptionItem('(UTC-05:00) Bogota, Lima, Quito, Rio Branco', 'SA Pacific Standard Time'),
  new OptionItem('(UTC-05:00) Chetumal', 'Eastern Standard Time (Mexico)'),
  new OptionItem('(UTC-05:00) Eastern Time (US & Canada)', 'Eastern Standard Time'),
  new OptionItem('(UTC-05:00) Haiti', 'Haiti Standard Time'),
  new OptionItem('(UTC-05:00) Havana', 'Cuba Standard Time'),
  new OptionItem('(UTC-05:00) Indiana (East)', 'US Eastern Standard Time'),
  new OptionItem('(UTC-05:00) Turks and Caicos', 'Turks and Caicos Standard Time'),
  new OptionItem('(UTC-04:00) Asuncion', 'Paraguay Standard Time'),
  new OptionItem('(UTC-04:00) Atlantic Time (Canada)', 'Atlantic Standard Time'),
  new OptionItem('(UTC-04:00) Caracas', 'Venezuela Standard Time'),
  new OptionItem('(UTC-04:00) Cuiaba', 'Central Brazilian Standard Time'),
  new OptionItem('(UTC-04:00) Georgetown, La Paz, Manaus, San Juan', 'SA Western Standard Time'),
  new OptionItem('(UTC-04:00) Santiago', 'Pacific SA Standard Time'),
  new OptionItem('(UTC-03:30) Newfoundland', 'Newfoundland Standard Time'),
  new OptionItem('(UTC-03:00) Araguaina', 'Tocantins Standard Time'),
  new OptionItem('(UTC-03:00) Brasilia', 'E. South America Standard Time'),
  new OptionItem('(UTC-03:00) Cayenne, Fortaleza', 'SA Eastern Standard Time'),
  new OptionItem('(UTC-03:00) City of Buenos Aires', 'Argentina Standard Time'),
  new OptionItem('(UTC-03:00) Greenland', 'Greenland Standard Time'),
  new OptionItem('(UTC-03:00) Montevideo', 'Montevideo Standard Time'),
  new OptionItem('(UTC-03:00) Punta Arenas', 'Magallanes Standard Time'),
  new OptionItem('(UTC-03:00) Saint Pierre and Miquelon', 'Saint Pierre Standard Time'),
  new OptionItem('(UTC-03:00) Salvador', 'Bahia Standard Time'),
  new OptionItem('(UTC-02:00) Coordinated Universal Time-02', 'UTC-02'),
  new OptionItem('(UTC-02:00) Mid-Atlantic - Old', 'Mid-Atlantic Standard Time'),
  new OptionItem('(UTC-01:00) Azores', 'Azores Standard Time'),
  new OptionItem('(UTC) Coordinated Universal Time', 'Coordinated Universal Time'),
  new OptionItem('(UTC+00:00) Dublin, Edinburgh, Lisbon, London', 'GMT Standard Time'),
  new OptionItem('(UTC+00:00) Monrovia, Reykjavik', 'Greenwich Standard Time'),
  new OptionItem('(UTC+00:00) Sao Tome', 'Sao Tome Standard Time'),
  new OptionItem('(UTC+01:00) Casablanca', 'Morocco Standard Time'),
  new OptionItem(
    '(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna',
    'W. Europe Standard Time',
  ),
  new OptionItem(
    '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague',
    'Central Europe Standard Time',
  ),
  new OptionItem('(UTC+01:00) Brussels, Copenhagen, Madrid, Paris', 'Romance Standard Time'),
  new OptionItem('(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb', 'Central European Standard Time'),
  new OptionItem('(UTC+01:00) West Central Africa', 'W. Central Africa Standard Time'),
];
