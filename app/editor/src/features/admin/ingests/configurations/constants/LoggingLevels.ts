import { OptionItem } from 'tno-core';

export const LoggingLevels = [
  new OptionItem('Quiet', 'quiet'),
  new OptionItem('Panic', 'panic'),
  new OptionItem('Fatal', 'fatal'),
  new OptionItem('Error', 'error'),
  new OptionItem('Warning', 'warning'),
  new OptionItem('Information', 'info'),
  new OptionItem('Verbose', 'verbose'),
  new OptionItem('Debug', 'debug'),
  new OptionItem('Trace', 'trace'),
];
