@inherits RazorEngineCore.RazorEngineTemplateBase<TNO.Services.Reporting.Models.ChartEngineContentModel>
@using System
@using System.Linq
@using TNO.Entities
@{
    // create a dictionary of dictionaries to flatten our data to something that can be accessed like this:
    // topicTypeAndCountByDate[date][topicType]
    Dictionary<DateTime, Dictionary<string, int>> topicTypeAndCountByDate = Content
         .Where(x => x.Topics.All(a => a.Name != "Not Applicable"))
         .GroupBy(i => i.PublishedOn.HasValue ? new DateTime(i.PublishedOn!.Value.Year, i.PublishedOn!.Value.Month, i.PublishedOn!.Value.Day) : DateTime.MinValue)
         .ToDictionary(
             g => g.Key,
             g => g.GroupBy(gg => gg.Topics.Any() ? gg.Topics.FirstOrDefault()!.TopicType.ToString() : "N/A")
                 .ToDictionary(
                     ggg => ggg.Key,
                     ggg => ggg.ToList().Count()));

     // get a list of all the distinct dates
     DateTime[] dates = topicTypeAndCountByDate.Keys.OrderBy(x => x.ToUniversalTime()).ToArray();

     // create a dictionary to store a value per day for each topic type
     Dictionary<string, List<int>> topicTypeDataSets = new Dictionary<string, List<int>>
     {
         {"Proactive", new List<int>()},
         {"Issues", new List<int>()}
     };
     // for each date in the returned dataset
     foreach (var date in dates)
     {
         // iterate over the possible topic type hits on a day
         foreach(var topicType in topicTypeDataSets.Keys)
         {
             int topicTypeHits = 0;
             // get the target topic type hits for the day, or use ZERO if not found
             if (topicTypeAndCountByDate[date].ContainsKey(topicType))
             {
                 topicTypeHits = topicTypeAndCountByDate[date][topicType];
             }
             topicTypeDataSets[topicType].Add(topicTypeHits);
         };
     };
     var topicTypeColorLookup = new Dictionary<string, string>
     {
         { "Proactive", "#006600" },
         { "Issues", "#BB1111" }
     };
     int index = 0;
     int topicTypeCount = topicTypeDataSets.Keys.ToArray().Length;
}
{
  "labels": [@String.Join(",", dates.Select(x => $"\"{x:dd-MMM}\""))],
  "datasets": [
    @foreach (var topicType in topicTypeDataSets)
    {
      @{
        var datasetColor = "";
        if (!topicTypeColorLookup.TryGetValue(topicType.Key, out datasetColor )) {
          datasetColor = "";
        }
      }

      @($"{{ \"label\": \"{topicType.Key}\", \"fill\": true, \"borderColor\": \"{@datasetColor}\",\"backgroundColor\": \"{@datasetColor}\", \"data\": [{@String.Join(",",topicType.Value)} ] }}")
      if (++index < topicTypeCount) @(",")
    }
   ]
}
