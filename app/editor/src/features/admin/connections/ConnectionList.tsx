import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useConnections } from 'store/hooks/admin';
import { Col, FlexboxTable, IconButton, IConnectionModel, Row } from 'tno-core';

import { ConnectionFilter } from './ConnectionFilter';
import { columns } from './constants';
import * as styled from './styled';

/**
 * Admin list view for connections.
 * @returns Component.
 */
const ConnectionList: React.FC = () => {
  const navigate = useNavigate();
  const [{ connections }, api] = useConnections();

  const [items, setItems] = React.useState<IConnectionModel[]>([]);

  React.useEffect(() => {
    if (!connections.length) {
      api.findAllConnections().then((data) => {
        setItems(data);
      });
    } else {
      setItems(connections);
    }
  }, [api, connections]);

  return (
    <styled.ConnectionList>
      <Row className="add-media" justifyContent="flex-end">
        <Col flex="1 1 0">
          Connections provide a way to configuration data storage locations and authentication
          settings. Ingest service configuration requires both a source and destination connection
          to be configured.
        </Col>
        <IconButton
          iconType="plus"
          label={`Add new connection`}
          onClick={() => navigate(`/admin/connections/0`)}
        />
      </Row>
      <ConnectionFilter
        onFilterChange={(filter) => {
          if (filter && filter.length) {
            const value = filter.toLocaleLowerCase();
            setItems(
              connections.filter(
                (i) =>
                  i.name.toLocaleLowerCase().includes(value) ||
                  i.description.toLocaleLowerCase().includes(value) ||
                  i.connectionType.toLocaleLowerCase().includes(value),
              ),
            );
          } else {
            setItems(connections);
          }
        }}
      />
      <FlexboxTable
        rowId="id"
        data={items}
        columns={columns}
        showSort={true}
        onRowClick={(row) => navigate(`${row.original.id}`)}
        pagingEnabled={false}
      />
    </styled.ConnectionList>
  );
};

export default ConnectionList;
