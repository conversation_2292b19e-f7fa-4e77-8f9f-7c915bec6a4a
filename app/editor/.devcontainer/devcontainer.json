// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.194.0/containers/typescript-node
{
	"name": "Node.js & TypeScript",
	"build": {
		"dockerfile": "Dockerfile",
		// Update 'VARIANT' to pick a Node version: 12, 14, 16
		"args": {
			"VARIANT": "18"
		}
	},
	"runArgs": ["--env-file", ".env"],
  "postCreateCommand": [
	],
  "postStartCommand": [
		"yarn"
	],
	// "appPort": 3000,
	"forwardPorts": [40082],
  // "mounts": [
  //   "source=D/SourceCode/quartech/tno/libs/npm/core,target=/workspaces/tno/app/editor/libs/core,type=bind,consistency=cached"
  // ],

  "customizations": {
    "vscode": {
      // Set *default* container specific settings.json values on container create.
      "settings": {},

      // Add the IDs of extensions you want installed when the container is created.
      "extensions": [
        "dbaeumer.vscode-eslint",
        "esbenp.prettier-vscode",
        "rvest.vs-code-prettier-eslint",
        "formulahendry.auto-close-tag",
        "cssho.vscode-svgviewer",
        "jpoissonnier.vscode-styled-components",
        "streetsidesoftware.code-spell-checker",
        "pflannery.vscode-versionlens",
        "styled-components.vscode-styled-components",
        "eamodio.gitlens",
        "chrisbibby.hide-node-modules",
        "kamikillerto.vscode-colorize",
        "naumovs.color-highlight",
        "syler.sass-indented",
        "michelemelluso.code-beautifier",
        "editorconfig.editorconfig",
        "firsttris.vscode-jest-runner"
      ]
    }
  },

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "yarn install",

	// Comment out connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "node"
}
