server {
    listen       8080;
    server_name  localhost;

    #access_log  /var/log/nginx/host.access.log  main;
    root   /usr/share/nginx/html;
    index  index.html index.htm;

    location /nginx-status {
        stub_status on;
        allow all;
        access_log off;
        default_type text/plain;
        return 200 "healthy\n";
    }

    location /api {
      client_max_body_size 5120M;
    }

    location / {
        try_files $uri /index.html;
        gzip            on;
        gzip_min_length 1000;
        gzip_types      *;

        sendfile off;
        expires -1;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
