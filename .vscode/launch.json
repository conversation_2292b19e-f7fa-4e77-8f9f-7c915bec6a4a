{
  "version": "0.2.0",
  "configurations": [


    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run API",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/api/net/bin/Debug/net9.0/TNO.API.dll",
      "args": [],
      "cwd": "${workspaceFolder}/api/net",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/api/net/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Syndication Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-syndication",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/syndication/bin/Debug/net9.0/TNO.Services.Syndication.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/syndication",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/syndication/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Content Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-content",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/content/bin/Debug/net9.0/TNO.Services.Content.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/content",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/content/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Content Migration Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-contentmigration",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/contentmigration/bin/Debug/net8.0/TNO.Services.ContentMigration.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/contentmigration",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/contentmigration/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Indexing Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-indexing",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/indexing/bin/Debug/net9.0/TNO.Services.Indexing.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/indexing",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/indexing/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Transcription Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-transcription",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/transcription/bin/Debug/net9.0/TNO.Services.Transcription.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/transcription",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/transcription/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run NLP Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-nlp",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/nlp/bin/Debug/net8.0/TNO.Services.NLP.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/nlp",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/nlp/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run FileMonitor Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-filemonitor",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/filemonitor/bin/Debug/net9.0/TNO.Services.FileMonitor.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/filemonitor",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/filemonitor/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Image Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-image",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/image/bin/Debug/net9.0/TNO.Services.Image.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/image",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/image/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Notification Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-notification",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/notification/bin/Debug/net9.0/TNO.Services.Notification.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/notification",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/notification/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Reporting Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-reporting",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/reporting/bin/Debug/net9.0/TNO.Services.Reporting.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/reporting",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/reporting/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Scheduler Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-scheduler",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/scheduler/bin/Debug/net9.0/TNO.Services.Scheduler.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/scheduler",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/scheduler/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Folder Collection Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-folder-collection",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/folder-collection/bin/Debug/net9.0/TNO.Services.FolderCollection.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/folder-collection",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/folder-collection/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run FFmpeg Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-ffmpeg",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/ffmpeg/bin/Debug/net9.0/TNO.Services.FFmpeg.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/ffmpeg",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/ffmpeg/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Extract Quotes Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-extract-quotes",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/extract-quotes/bin/Debug/net9.0/TNO.Services.ExtractQuotes.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/extract-quotes",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/extract-quotes/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run EventHandler Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-event-handler",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/event-handler/bin/Debug/net9.0/TNO.Services.EventHandler.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/event-handler",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/event-handler/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Ches Retry Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-ches-retry",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/services/net/ches-retry/bin/Debug/net9.0/TNO.Services.ChesRetry.dll",
      "args": [],
      "cwd": "${workspaceFolder}/services/net/ches-retry",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/services/net/ches-retry/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Elastic Migration Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-elastic-migration",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/tools/elastic/migration/bin/Debug/net9.0/TNO.Elastic.Migration.dll",
      "args": [],
      "cwd": "${workspaceFolder}/tools/elastic/migration",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/tools/elastic/migration/.env"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Run Elastic Indexer Service",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-elastic-indexer",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/tools/indexer/bin/Debug/net9.0/TNO.Tools.ElasticIndexer.dll",
      "args": [],
      "cwd": "${workspaceFolder}/tools/indexer",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false,
      "envFile": "${workspaceFolder}/tools/indexer/.env"
    },
    {
      "name": ".NET Core Attach",
      "type": "coreclr",
      "request": "attach"
    }
  ]
}
