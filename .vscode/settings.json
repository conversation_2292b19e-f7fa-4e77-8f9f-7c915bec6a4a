{
  "git.ignoreLimitWarning": true,
  "yaml.schemas": {
    "kubernetes": "kubernetes.yml",
    "http://json.schemastore.org/appveyor": "appveyor.yml",
    "https://json.schemastore.org/github-workflow": "/.github/workflows/**/*.yml",
    "https://raw.githubusercontent.com/garethr/openshift-json-schema/master/v4.1.0/_definitions.json": "./openshift/**/*.yaml",
  },
  "yaml.schemaStore.enable": false,
  "cSpell.words": [
    "CBRA",
    "CHES",
    "datalabels",
    "formik",
    "Idir",
    "insertable",
    "Keycloak",
    "kowl",
    "Kustomization",
    "kustomize",
    "notnull",
    "opennlp",
    "Renci",
    "Swashbuckle",
    "Unpublish"
  ],
  "java.configuration.updateBuildConfiguration": "interactive",
  "editor.codeActionsOnSave": {
    "source.organizeImports": "never",
    "source.fixAll.eslint": "explicit"
  },
  "omnisharp.enableRoslynAnalyzers": true,
  "omnisharp.enableMsBuildLoadProjectsOnDemand": true,
  "dotnet.server.useOmnisharp": true,
  "omnisharp.analyzeOpenDocumentsOnly": true,
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/node_modules": false
  },
  "dotnet.defaultSolution": "./TNO.sln",
  "dotnet.formatting.organizeImportsOnFormat": true,
}
