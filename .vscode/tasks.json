{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "build",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/api/net/TNO.API.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/api/net/TNO.API.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/api/net/TNO.API.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-syndication",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/syndication/TNO.Services.Syndication.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-syndication",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/syndication/TNO.Services.Syndication.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-syndication",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/syndication/TNO.Services.Syndication.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-content",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/content/TNO.Services.Content.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-content",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/content/TNO.Services.Content.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-content",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/content/TNO.Services.Content.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-contentmigration",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/contentmigration/TNO.Services.ContentMigration.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-contentmigration",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/contentmigration/TNO.Services.ContentMigration.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-contentmigration",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/contentmigration/TNO.Services.ContentMigration.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-indexing",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/indexing/TNO.Services.Indexing.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-indexing",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/indexing/TNO.Services.Indexing.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-indexing",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/indexing/TNO.Services.Indexing.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-transcription",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/transcription/TNO.Services.Transcription.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-transcription",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/transcription/TNO.Services.Transcription.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-transcription",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/transcription/TNO.Services.Transcription.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-nlp",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/nlp/TNO.Services.NLP.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-nlp",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/nlp/TNO.Services.NLP.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-nlp",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/nlp/TNO.Services.NLP.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-filemonitor",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/filemonitor/TNO.Services.FileMonitor.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-filemonitor",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/filemonitor/TNO.Services.FileMonitor.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-filemonitor",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/filemonitor/TNO.Services.FileMonitor.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-image",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/image/TNO.Services.Image.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-image",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/image/TNO.Services.Image.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-image",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/image/TNO.Services.Image.csproj"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-elastic-migration",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/tools/elastic/migration/TNO.Elastic.Migration.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-elastic-migration",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/tools/elastic/migration/TNO.Elastic.Migration.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-elastic-migration",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/tools/elastic/migration/TNO.Elastic.Migration.csproj",
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-notification",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/notification/TNO.Services.Notification.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-notification",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/notification/TNO.Services.Notification.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-notification",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/notification/TNO.Services.Notification.csproj",
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-reporting",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/reporting/TNO.Services.Reporting.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-reporting",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/reporting/TNO.Services.Reporting.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-reporting",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/reporting/TNO.Services.Reporting.csproj",
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-scheduler",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/scheduler/TNO.Services.Scheduler.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-scheduler",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/scheduler/TNO.Services.Scheduler.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-scheduler",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/scheduler/TNO.Services.Scheduler.csproj",
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-folder-collection",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/folder-collection/TNO.Services.FolderCollection.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-folder-collection",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/folder-collection/TNO.Services.FolderCollection.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-folder-collection",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/folder-collection/TNO.Services.FolderCollection.csproj",
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-ffmpeg",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/ffmpeg/TNO.Services.FFmpeg.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-ffmpeg",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/ffmpeg/TNO.Services.FFmpeg.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-ffmpeg",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/ffmpeg/TNO.Services.FFmpeg.csproj",
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-extract-quotes",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/extract-quotes/TNO.Services.ExtractQuotes.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-extract-quotes",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/extract-quotes/TNO.Services.ExtractQuotes.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-extract-quotes",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/extract-quotes/TNO.Services.ExtractQuotes.csproj",
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-event-handler",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/event-handler/TNO.Services.EventHandler.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-event-handler",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/event-handler/TNO.Services.EventHandler.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-event-handler",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/event-handler/TNO.Services.EventHandler.csproj",
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "build-ches-retry",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/services/net/ches-retry/TNO.Services.ChesRetry.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-ches-retry",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/services/net/ches-retry/TNO.Services.ChesRetry.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-ches-retry",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/services/net/ches-retry/TNO.Services.ChesRetry.csproj",
      ],
      "problemMatcher": "$msCompile"
    }
    ,
    {
      "label": "build-elastic-indexer",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/tools/indexer/TNO.Tools.ElasticIndexer.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish-elastic-indexer",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/tools/indexer/TNO.Tools.ElasticIndexer.csproj",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch-elastic-indexer",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/tools/indexer/TNO.Tools.ElasticIndexer.csproj",
      ],
      "problemMatcher": "$msCompile"
    }
  ]
}
