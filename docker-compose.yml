services:
  ####################### Database #######################
  database:
    image: tno:database
    profiles:
      - all
      - api
      - editor
      - subscriber
      - data
      - ingest
      - init
      - service
      - keycloak
      - main
    restart: "no"
    container_name: tno-database
    build:
      context: ${DB_CONTEXT:-db/postgres/docker}
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
        reservations:
          cpus: '0.10'
          memory: 50M
    env_file:
      - ${DB_CONTEXT:-db/postgres/docker}/.env
    ports:
      - ${DATABASE_PORT:-40000}:5432
    volumes:
      - tno-database-data:${DB_VOLUME:-/var/lib/postgresql/data}
    networks:
      - tno

  ####################### Keycloak #######################
  keycloak:
    image: tno:keycloak
    profiles:
      - all
      - api
      - editor
      - subscriber
      - init
      - service
      - keycloak
      - main
    restart: "no"
    container_name: tno-keycloak
    build:
      context: ./auth/keycloak
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 500M
        reservations:
          cpus: '0.05'
          memory: 50M
    env_file:
      - auth/keycloak/.env
    volumes:
      - ./auth/keycloak/config:/opt/keycloak/data/import
    ports:
      - ${KEYCLOAK_HTTP_PORT:-40001}:8080
      - ${KEYCLOAK_HTTPS_PORT:-40002}:443
    depends_on:
      - database
    networks:
      - tno
    healthcheck:
      test: curl -s -f http://localhost:8080/auth/realms/tno || exit 1
      interval: 5m
      timeout: 15s
      retries: 3
      start_period: 1m

  ####################### Elasticsearch #######################
  elastic:
    image: tno:elastic
    profiles:
      - all
      - editor
      - subscriber
      - elastic
      - utility
      - data
      - init
      - service
      - main
    restart: "no"
    container_name: tno-elastic
    build:
      context: db/elasticsearch
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '0.10'
          memory: 250M
    env_file:
      - db/elasticsearch/.env
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - ${ELASTIC_HTTP_PORT:-40003}:9200
      - ${ELASTIC_COM_PORT:-40004}:9300
    volumes:
      - tno-elastic-data:/usr/share/elasticsearch/data
    networks:
      - tno
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "-u '$$ELASTIC_USERNAME:$$ELASTIC_PASSWORD'", "http://localhost:9200/_cat/health"]
    #   # test: curl -f -u '${ELASTIC_USERNAME}:${ELASTIC_PASSWORD}' http://localhost:9200/_cat/health
    #   interval: 1m
    #   timeout: 10s
    #   retries: 3
    #   start_period: 15s


  indexer:
    profiles:
      - indexer
    build:
      context: ./
      dockerfile: tools/indexer/Dockerfile
    env_file:
      - tools/indexer/.env
    networks:
      - tno

  ####################### API #######################
  api:
    image: tno:api
    profiles:
      - all
      - api
      - editor
      - subscriber
      - service
      - main
    restart: "no"
    container_name: tno-api
    build:
      context: ./
      dockerfile: api/net/Dockerfile
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 250M
    env_file: api/net/.env
    ports:
      - ${API_HTTP_PORT:-40010}:8080
      - ${API_HTTPS_PORT:-40011}:443
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - database
      - keycloak
    volumes:
      - tno-api-data:/data
      - tno-av-data:/av
    networks:
      - tno
    healthcheck:
      test: curl -s -f http://localhost:8080/health
      interval: 5m
      timeout: 30s
      retries: 3
      start_period: 2m


  ####################### Charts API #######################
  charts:
    image: tno:charts
    profiles:
      - all
      - api
      - editor
      - subscriber
      - service
      - main
    restart: "no"
    container_name: tno-charts
    build:
      context: api/node
      dockerfile: Dockerfile.local
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 1G
        reservations:
          cpus: '0.10'
          memory: 250M
    env_file: api/node/.env
    ports:
      - ${CHARTS_HTTP_PORT:-40014}:8080
      - ${CHARTS_HTTPS_PORT:-40015}:443
    extra_hosts:
      - host.docker.internal:host-gateway
    volumes:
      - ./api/node/public:/usr/app/public
      - ./api/node/src:/usr/app/src
    networks:
      - tno

  ####################### App - Editor #######################
  editor:
    image: tno:editor
    profiles:
      - all
      - editor
      - service
      - main
    stdin_open: true
    tty: true
    restart: "no"
    container_name: tno-editor
    build:
      context: app/editor
      dockerfile: Dockerfile
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '0.10'
          memory: 500M
    env_file: app/editor/.env
    volumes:
      - ./app/editor/src:/usr/app/src
      - ./app/editor/public:/usr/app/public
    ports:
      - ${APP_EDITOR_HTTP_PORT:-40082}:3000
      - ${APP_EDITOR_HTTPS_PORT:-40444}:3443
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - api
    networks:
      - tno

  ####################### App - Subscriber #######################
  subscriber:
    image: tno:subscriber
    profiles:
      - all
      - subscriber
      - service
      - main
    stdin_open: true
    tty: true
    restart: "no"
    container_name: tno-subscriber
    build:
      context: app/subscriber
      dockerfile: Dockerfile
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '0.10'
          memory: 500M
    env_file: ./app/subscriber/.env
    volumes:
      - ./app/subscriber/src:/usr/app/src
      - ./app/subscriber/public:/usr/app/public
    ports:
      - ${APP_SUBSCRIBER_HTTP_PORT:-40083}:3000
      - ${APP_SUBSCRIBER_HTTPS_PORT:-40445}:3443
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - api
    networks:
      - tno

  ####################### Nginx #######################
  nginx:
    image: tno:nginx
    profiles:
      - all
      - api
      - editor
      - subscriber
      - ingest
      - service
      - main
    restart: "no"
    container_name: tno-nginx
    build:
      context: ./network/nginx
    env_file:
      - network/nginx/.env
    ports:
      - ${NGINX_HTTP_EDITOR_PORT:-40080}:80
      - ${NGINX_HTTP_SUBSCRIBER_PORT:-40081}:81
      - ${NGINX_HTTPS_PORT:-40443}:443
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - api
      - editor
      - subscriber
    networks:
      - tno

  ####################### API #######################
  ssh:
    image: linuxserver/openssh-server
    profiles:
      - all
    restart: "no"
    container_name: tno-ssh
    environment:
      - PUID=1000
      - PGID=1000
      - USER_NAME=user
      - USER_PASSWORD=password
      - PASSWORD_ACCESS=true
    ports:
      - 2222:2222
    volumes:
      - tno-ssh-data:/mnt/av
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - tno
  ####################### back-service #######################
  backup-service:
    profiles:
      - all
    build:
      context: tools/objects-backup
      dockerfile: Dockerfile
    volumes:
      - tno-av-data:/av:ro
    env_file:
      - tools/objects-backup/.env
    command: ["/bin/bash", "/scripts/entrypoint.sh"]
    networks:
      - tno
  ####################### Oracle #######################
  oracle:
    profiles:
      - all
      - utility
      - service
    restart: "no"
    container_name: tno-oracle
    build:
      context: ${ORACLE_DB_CONTEXT:-db/oracle/docker}
    env_file:
      - ${ORACLE_DB_CONTEXT:-db/oracle/docker}/.env
    ports:
      - ${ORACLE_DATABASE_PORT:-41521}:1521
    volumes:
      - tno-oracle-data:/opt/oracle/oradata
      - /d/TNO-Backup:/data
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - tno

  oracleclient:
    profiles:
      - all
      - utility
    restart: "no"
    container_name: tno-oracleclient
    build:
      context: tools/oracle-instantclient
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - tno

####################### CoreNLP #######################
  corenlp:
    profiles:
      - all
      - utility
      - init
      - service
    restart: "no"
    container_name: tno-corenlp
    build:
      context: tools/corenlp
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 4G
        reservations:
          cpus: '0.50'
          memory: 2G
    ports:
      - ${CORENLP_PORT:-40038}:9000
    extra_hosts:
      - host.docker.internal:host-gateway
    networks:
      - tno

####################### Volumes Definition #######################
volumes:
  tno-database-data:
    name: tno-database-data
  tno-elastic-data:
    name: tno-elastic-data
  tno-api-data:
    name: tno-api-data
  tno-av-data:
    name: tno-av-data
  tno-ssh-data:
    name: tno-ssh-data
  tno-oracle-data:
    name: tno-oracle-data

# To inspect healthcheck
# docker inspect --format='{{json .State.Health}}' tno-nlp

