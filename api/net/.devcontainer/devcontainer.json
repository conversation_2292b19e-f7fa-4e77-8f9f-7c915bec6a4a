// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.224.2/containers/dotnet
{
	"name": "C# (.NET)",
	"build": {
		"dockerfile": "Dockerfile",
		"args": {
			// Update 'VARIANT' to pick a .NET Core version: 3.1, 5.0, 6.0, 7.0
			// Append -bullseye or -focal to pin to an OS version.
			"VARIANT": "9.0",
			// Options
			"NODE_VERSION": "lts/*"
		}
	},

  "customizations": {
    "vscode": {
      // Set *default* container specific settings.json values on container create.
      "settings": {},

      // Add the IDs of extensions you want installed when the container is created.
      "extensions": [
        "ms-dotnettools.csharp",
        "eamodio.gitlens",
        "jorgeserrano.vscode-csharp-snippets",
        "adrianwil<PERSON><PERSON>ski.add-reference",
        "mikestead.dotenv",
        "editorconfig.editorconfig",
        "patcx.vscode-nuget-gallery",
        "jmrog.vscode-nuget-package-manager",
        "aliasadidev.nugetpackagemanagergui",
        "pflannery.vscode-versionlens",
        "kreativ-software.csharpextensions"
      ]
    }
  },

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [8080],

	// [Optional] To reuse of your local HTTPS dev cert:
	//
	// 1. Export it locally using this command:
	//    * Windows PowerShell:
	//        dotnet dev-certs https --trust; dotnet dev-certs https -ep "$env:USERPROFILE/.aspnet/https/aspnetapp.pfx" -p "SecurePwdGoesHere"
	//    * macOS/Linux terminal:
	//        dotnet dev-certs https --trust; dotnet dev-certs https -ep "${HOME}/.aspnet/https/aspnetapp.pfx" -p "SecurePwdGoesHere"
	//
	// 2. Uncomment these 'remoteEnv' lines:
	//    "remoteEnv": {
	// 	      "ASPNETCORE_Kestrel__Certificates__Default__Password": "SecurePwdGoesHere",
	//        "ASPNETCORE_Kestrel__Certificates__Default__Path": "/home/<USER>/.aspnet/https/aspnetapp.pfx",
	//    },
	//
	// 3. Do one of the following depending on your scenario:
	//    * When using GitHub Codespaces and/or Remote - Containers:
	//      1. Start the container
	//      2. Drag ~/.aspnet/https/aspnetapp.pfx into the root of the file explorer
	//      3. Open a terminal in VS Code and run "mkdir -p /home/<USER>/.aspnet/https && mv aspnetapp.pfx /home/<USER>/.aspnet/https"
	//
	//    * If only using Remote - Containers with a local container, uncomment this line instead:
	//      "mounts": [ "source=${env:HOME}${env:USERPROFILE}/.aspnet/https,target=/home/<USER>/.aspnet/https,type=bind" ],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "dotnet restore",

	// Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "vscode",
	"features": {
		"git": "latest"
	}
}
