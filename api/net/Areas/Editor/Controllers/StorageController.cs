using System.Net;
using System.Net.Mime;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.Annotations;
using TNO.API.Areas.Editor.Models.Storage;
using TNO.API.Config;
using TNO.API.Helpers;
using TNO.API.Models;
using TNO.Core.Exceptions;
using TNO.Core.Extensions;
using TNO.Core.Storage;
using TNO.Core.Storage.Configuration;
using TNO.DAL.Config;
using TNO.DAL.Helpers;
using TNO.DAL.Services;
using TNO.Entities;
using TNO.Keycloak;
using TNO.Models.Extensions;

namespace TNO.API.Areas.Editor.Controllers;

/// <summary>
/// StorageController class, provides Storage endpoints for the api.
/// </summary>
[ClientRoleAuthorize(ClientRole.Editor)]
[ApiController]
[Area("editor")]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[area]/storage")]
[Route("api/[area]/storage")]
[Route("v{version:apiVersion}/[area]/storage")]
[Route("[area]/storage")]
[ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.Unauthorized)]
[ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.Forbidden)]
public class StorageController : ControllerBase
{
    #region Variables
    private readonly IConnectionHelper _connection;
    private readonly StorageOptions _storageOptions;
    private readonly ApiOptions _apiOptions;
    private readonly IFileReferenceService _fileReferenceService;
    private readonly IS3StorageService _s3StorageService;
    private readonly ILogger<StorageController> _logger;
    private readonly S3Options _s3Options;
    #endregion

    #region Constructors
    /// <summary>
    /// Creates a new instance of a StorageController object, initializes with specified parameters.
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="storageOptions"></param>
    /// <param name="apiOptions"></param>
    /// <param name="fileReferenceService"></param>
    /// <param name="logger"></param>
    /// <param name="s3Options"></param>
    /// <param name="s3StorageService"></param>
    public StorageController(
        IConnectionHelper connection,
        IOptions<StorageOptions> storageOptions,
        IOptions<ApiOptions> apiOptions,
        IFileReferenceService fileReferenceService,
        ILogger<StorageController> logger,
        IOptions<S3Options> s3Options,
        IS3StorageService s3StorageService)
    {
        _connection = connection;
        _storageOptions = storageOptions.Value;
        _apiOptions = apiOptions.Value;
        _fileReferenceService = fileReferenceService;
        _logger = logger;
        _s3Options = s3Options.Value;
        _s3StorageService = s3StorageService;
    }
    #endregion

    #region Endpoints
    /// <summary>
    /// Fetch an array of files and directories at the specified path.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    [HttpGet]
    [HttpGet("{locationId:int}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(DirectoryModel), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NoContent)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public IActionResult GetDirectory([FromRoute] int? locationId, [FromQuery] string? path)
    {
        path = String.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            // TODO: Handle multiple storage locations.
            if (dataLocation.Connection?.ConnectionType == ConnectionType.SSH)
            {
                var configuration = _connection.GetConfiguration(dataLocation.Connection);
                var locationPath = configuration.GetDictionaryJsonValue<string>("path") ?? "";
                using var client = _connection.CreateSftpClient(configuration);
                try
                {
                    client.Connect();

                    if (!client.Exists(Path.Combine(locationPath, path))) return new NoContentResult();

                    var files = client.ListDirectory(Path.Combine(locationPath, path));
                    return new JsonResult(new DirectoryModel(files, _apiOptions.DataLocation == dataLocation?.Name));
                }
                finally
                {
                    if (client.IsConnected)
                        client.Disconnect();
                }
            }
            else if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                return new JsonResult(new DirectoryModel(_storageOptions.GetCapturePath(), path, true));
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            return new JsonResult(new DirectoryModel(_storageOptions.GetUploadPath(), path, true));
        }
    }

    /// <summary>
    /// Fetch an array of files and directories at the specified path.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    [HttpGet("exists")]
    [HttpGet("{locationId:int}/exists")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NoContent)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public IActionResult DirectoryExists([FromRoute] int? locationId, [FromQuery] string? path)
    {
        path = String.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            // TODO: Handle multiple storage locations.
            if (dataLocation.Connection?.ConnectionType == ConnectionType.SSH)
            {
                var configuration = _connection.GetConfiguration(dataLocation.Connection);
                var locationPath = configuration.GetDictionaryJsonValue<string>("path") ?? "";
                using var client = _connection.CreateSftpClient(configuration);
                try
                {
                    client.Connect();
                    return client.Exists(Path.Combine(locationPath, path)) ? new OkResult() : new NoContentResult();
                }
                finally
                {
                    if (client.IsConnected)
                        client.Disconnect();
                }
            }
            else if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                return Directory.Exists(Path.Combine(_storageOptions.GetCapturePath(), path)) ? new OkResult() : new NoContentResult();
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            return Directory.Exists(Path.Combine(_storageOptions.GetUploadPath(), path)) ? new OkResult() : new NoContentResult();
        }

    }

    /// <summary>
    /// Upload a file and link it to the specified content.
    /// Only a single file can be linked to content, each upload will overwrite.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <param name="files"></param>
    /// <param name="overwrite"></param>
    /// <returns></returns>
    [HttpPost("upload")]
    [HttpPost("{locationId:int}/upload")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(ItemModel), (int)HttpStatusCode.Created)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public async Task<IActionResult> Upload([FromRoute] int? locationId, [FromQuery] string path, [FromForm] List<IFormFile> files, bool overwrite = false)
    {
        if (files.Count == 0) throw new InvalidOperationException("File missing");
        var file = files.First();
        path = String.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                var safePath = Path.Combine(_storageOptions.GetCapturePath(), path, file.FileName);
                if (String.IsNullOrWhiteSpace(Path.GetFileName(safePath))) throw new InvalidOperationException("Filename missing");
                if (safePath.DirectoryExists()) throw new InvalidOperationException("Invalid path");

                var directory = Path.GetDirectoryName(safePath);
                if (directory?.DirectoryExists() == false)
                    Directory.CreateDirectory(directory);

                if (!overwrite && safePath.FileExists()) throw new InvalidOperationException("File already exists");

                // TODO: Handle multiple files.
                using var stream = System.IO.File.Open(safePath, FileMode.Create);
                await file.CopyToAsync(stream);

                return new JsonResult(new ItemModel(safePath, true));
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            var safePath = Path.Combine(_storageOptions.GetUploadPath(), path, file.FileName);
            if (String.IsNullOrWhiteSpace(Path.GetFileName(safePath))) throw new InvalidOperationException("Filename missing");
            if (safePath.DirectoryExists()) throw new InvalidOperationException("Invalid path");

            var directory = Path.GetDirectoryName(safePath);
            if (directory?.DirectoryExists() == false)
                Directory.CreateDirectory(directory);

            if (!overwrite && safePath.FileExists()) throw new InvalidOperationException("File already exists");

            // TODO: Handle multiple files.
            using var stream = System.IO.File.Open(safePath, FileMode.Create);
            await file.CopyToAsync(stream);

            return new JsonResult(new ItemModel(safePath, true));
        }
    }

    /// <summary>
    /// Stream the file for the specified path.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    [HttpGet("stream")]
    [HttpGet("{locationId:int}/stream")]
    [ProducesResponseType(typeof(FileStreamResult), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(FileStreamResult), (int)HttpStatusCode.PartialContent)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public async Task<IActionResult> StreamAsync([FromRoute] int? locationId, [FromQuery] string path)
    {
        path = string.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                var safePath = Path.Combine(_storageOptions.GetCapturePath(), path);

                return await GetResultAsync(safePath, path);
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            _logger.LogInformation("Getting stream for path: {Path}", path);
            var safePath = Path.Combine(_storageOptions.GetUploadPath(), path);
            return await GetResultAsync(safePath, path);
        }
    }


    private async Task<IActionResult> GetResultAsync(string safePath, string path)
    {
        //find file from s3
        var stream = await _s3StorageService.DownloadFromS3Async(path);
        if (stream != null)
        {
            return File(stream, "application/octet-stream");
        }

        if (!safePath.FileExists()) throw new NoContentException($"Stream does not exist: '{path}'");
        var info = new ItemModel(safePath, true);
        var fileStream = System.IO.File.OpenRead(safePath);
        return File(fileStream, info.MimeType!);
    }

    /// <summary>
    /// Download the file for the specified path.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    [HttpGet("download")]
    [HttpGet("{locationId:int}/download")]
    [Produces("application/octet-stream")]
    [ProducesResponseType(typeof(FileStreamResult), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public IActionResult Download([FromRoute] int? locationId, [FromQuery] string path)
    {
        path = String.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                var safePath = Path.Combine(_storageOptions.GetCapturePath(), path);
                if (!safePath.FileExists() && !safePath.DirectoryExists()) throw new InvalidOperationException($"File/folder does not exist: '{path}'");

                // TODO: download a full folder as a ZIP
                var info = new ItemModel(safePath, true);
                var stream = System.IO.File.OpenRead(safePath);
                return File(stream, contentType: info.MimeType!, fileDownloadName: info.Name, enableRangeProcessing: false);
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            var safePath = Path.Combine(_storageOptions.GetUploadPath(), path);
            if (!safePath.FileExists() && !safePath.DirectoryExists()) throw new InvalidOperationException($"File/folder does not exist: '{path}'");

            // TODO: download a full folder as a ZIP
            var info = new ItemModel(safePath, true);
            var stream = System.IO.File.OpenRead(safePath);
            return File(stream, contentType: info.MimeType!, fileDownloadName: info.Name, enableRangeProcessing: false);
        }
    }

    /// <summary>
    /// Moves a file from specified 'path' to 'destination'.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <param name="destination"></param>
    /// <returns></returns>
    [HttpPut("move")]
    [HttpPut("{locationId:int}/move")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(ItemModel), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public IActionResult Move([FromRoute] int? locationId, [FromQuery] string path, [FromQuery] string destination)
    {
        path = String.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                var rootPath = _storageOptions.GetCapturePath();
                var safePath = Path.Combine(rootPath, path.MakeRelativePath());
                if (!safePath.FileExists() && !safePath.DirectoryExists()) throw new InvalidOperationException($"File does not exist: '{path}'");

                var safeDestination = Path.Combine(rootPath, destination.MakeRelativePath());
                if (safeDestination.FileExists()) throw new InvalidOperationException($"File already exists, cannot rename: '{destination}'");

                var directory = Path.GetDirectoryName(safeDestination);
                if (directory?.DirectoryExists() == false)
                    Directory.CreateDirectory(directory);

                System.IO.File.Move(safePath, safeDestination);
                var info = new ItemModel(safeDestination, true);
                return new JsonResult(info);
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            var rootPath = _storageOptions.GetUploadPath();
            var safePath = Path.Combine(rootPath, path.MakeRelativePath());
            if (!safePath.FileExists() && !safePath.DirectoryExists()) throw new InvalidOperationException($"File does not exist: '{path}'");

            var safeDestination = Path.Combine(rootPath, destination.MakeRelativePath());
            if (safeDestination.FileExists()) throw new InvalidOperationException($"File already exists, cannot rename: '{destination}'");

            var directory = Path.GetDirectoryName(safeDestination);
            if (directory?.DirectoryExists() == false)
                Directory.CreateDirectory(directory);

            System.IO.File.Move(safePath, safeDestination);
            var info = new ItemModel(safeDestination, true);
            return new JsonResult(info);
        }
    }

    /// <summary>
    /// Delete file at path.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    [HttpDelete]
    [HttpDelete("{locationId:int}")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(ItemModel), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public IActionResult Delete([FromRoute] int? locationId, [FromQuery] string path)
    {
        path = String.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            // TODO: Handle multiple storage locations.
            if (dataLocation.Connection?.ConnectionType == ConnectionType.SSH)
            {
                var configuration = _connection.GetConfiguration(dataLocation.Connection);
                var locationPath = configuration.GetDictionaryJsonValue<string>("path") ?? "";
                using var client = _connection.CreateSftpClient(configuration);
                try
                {
                    client.Connect();
                    var safePath = Path.Combine(locationPath, path);
                    if (!client.Exists(safePath)) throw new InvalidOperationException($"File does not exist: '{path}'");

                    var info = new ItemModel(Path.GetFileName(safePath), client.GetAttributes(safePath), _apiOptions.DataLocation == dataLocation?.Name);
                    client.Delete(safePath);
                    return new JsonResult(info);
                }
                finally
                {
                    if (client.IsConnected)
                        client.Disconnect();
                }
            }
            else if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                var safePath = Path.Combine(_storageOptions.GetCapturePath(), path.MakeRelativePath());
                if (!safePath.FileExists() && !safePath.DirectoryExists()) throw new InvalidOperationException($"File/folder does not exist: '{path}'");

                // TODO: Only certain users should be allowed to delete certain files/folders.
                var item = new ItemModel(safePath, true);
                if (item.IsDirectory) Directory.Delete(safePath);
                else System.IO.File.Delete(safePath);
                return new JsonResult(item);
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            var safePath = Path.Combine(_storageOptions.GetUploadPath(), path.MakeRelativePath());
            if (!safePath.FileExists() && !safePath.DirectoryExists()) throw new InvalidOperationException($"File/folder does not exist: '{path}'");

            // TODO: Only certain users should be allowed to delete certain files/folders.
            var item = new ItemModel(safePath, true);
            if (item.IsDirectory) Directory.Delete(safePath);
            else System.IO.File.Delete(safePath);
            return new JsonResult(item);
        }
    }

    /// <summary>
    /// Make a clip from the target file identified in the clip parameter 'prefix'.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <param name="start"></param>
    /// <param name="end"></param>
    /// <param name="outputName"></param>
    /// <returns></returns>
    [HttpPost("clip")]
    [HttpPost("{locationId:int}/clip")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(ItemModel), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public async Task<IActionResult> CreateClipAsync([FromRoute] int? locationId, [FromQuery] string path, [FromQuery] int start, [FromQuery] int end, [FromQuery] string outputName)
    {
        path = String.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                var safePath = Path.Combine(_storageOptions.GetCapturePath(), path.MakeRelativePath());
                var file = await FfmpegHelper.CreateClipAsync(safePath, start, end, outputName);
                return new JsonResult(new ItemModel(file, true));
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            var safePath = Path.Combine(_storageOptions.GetUploadPath(), path.MakeRelativePath());
            var file = await FfmpegHelper.CreateClipAsync(safePath, start, end, outputName);
            return new JsonResult(new ItemModel(file, true));
        }
    }

    /// <summary>
    /// Make a clip from the target file identified in the clip parameter 'prefix'.
    /// </summary>
    /// <param name="locationId"></param>
    /// <param name="path"></param>
    /// <param name="prefix"></param>
    /// <returns></returns>
    [HttpPost("join")]
    [HttpPost("{locationId:int}/join")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(ItemModel), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public async Task<IActionResult> JoinClipsAsync([FromRoute] int? locationId, [FromQuery] string path, [FromQuery] string prefix)
    {
        path = String.IsNullOrWhiteSpace(path) ? "" : HttpUtility.UrlDecode(path).GetDirectoryPath().MakeRelativePath();
        var dataLocation = locationId.HasValue ? _connection.GetDataLocation(locationId.Value) : null;
        if (dataLocation != null)
        {
            if (dataLocation.Connection == null || dataLocation.Connection?.ConnectionType == ConnectionType.LocalVolume)
            {
                var safePath = Path.Combine(_storageOptions.GetCapturePath(), path.MakeRelativePath());
                var file = await FfmpegHelper.JoinClipsAsync(safePath, prefix);
                return new JsonResult(new ItemModel(file, true));
            }
            else throw new NotImplementedException($"Location connection type '{dataLocation.Connection?.ConnectionType}' not implemented yet.");
        }
        else
        {
            var safePath = Path.Combine(_storageOptions.GetUploadPath(), path.MakeRelativePath());
            var file = await FfmpegHelper.JoinClipsAsync(safePath, prefix);
            return new JsonResult(new ItemModel(file, true));
        }
    }
    /// <summary>
    /// upload files to s3
    /// </summary>
    /// <param name="publishedAfter">optional, only upload files's content published after the specified date</param>
    /// <param name="publishedBefore">optional, only upload files's content published before the specified date</param>
    /// <param name="limit">optional, only upload limit files</param>
    /// <param name="force">optional, force upload files</param>
    /// <returns>uploaded files and failed uploads</returns>
    [HttpPost("upload-files-to-s3")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(Dictionary<string, List<string>>), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public async Task<IActionResult> UploadFileToS3Async([FromQuery] DateTime? publishedAfter = null, [FromQuery] DateTime? publishedBefore = null, [FromQuery] int? limit = null, [FromQuery] bool force = false)
    {
        _logger.LogDebug("upload-files-to-s3");
        var fileReferences = await _fileReferenceService.GetFiles(publishedAfter, publishedBefore, limit ?? 100, force);
        var uploadedFiles = new List<string>();
        var failedUploads = new List<string>();
        // check if s3 credentials are set
        if (!_s3Options.IsS3Enabled)
        {
            return BadRequest("S3 is not enabled or credentials are not set");
        }

        foreach (var fileReference in fileReferences)
        {
            try
            {
                var filePath = Path.Combine(_storageOptions.GetUploadPath(), fileReference.Path);

                if (System.IO.File.Exists(filePath))
                {
                    using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                    // use relative path as S3 key
                    var s3Key = fileReference.Path.Replace("\\", "/"); // make sure use forward slash as path separator
                    _logger.LogDebug($"uploading: {s3Key}");
                    var uploadSuccess = await _s3StorageService.UploadToS3Async(s3Key, fileStream);

                    if (uploadSuccess)
                    {
                        // update file reference with s3 path
                        fileReference.S3Path = s3Key;
                        fileReference.IsSyncedToS3 = true;
                        fileReference.LastSyncedToS3On = DateTime.UtcNow;
                        await _fileReferenceService.UpdateAsync(fileReference);

                        uploadedFiles.Add(s3Key);

                        if (fileReference.ContentType.StartsWith("video/") || fileReference.ContentType.StartsWith("audio/"))
                        {
                            try
                            {
                                System.IO.File.Delete(filePath);
                                _logger.LogDebug("deleted local file: {FilePath}, contentId: {ContentId}", filePath, fileReference.ContentId);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "failed to delete local file: {FilePath}, contentId: {ContentId}", filePath, fileReference.ContentId);
                            }
                        }
                    }
                }
                else
                {
                    failedUploads.Add($"{fileReference.Path} (file not found)");
                    if (!fileReference.IsSyncedToS3)
                    {
                        _logger.LogDebug("File not found, broken reference deleted: {Path}, contentId: {ContentId}", fileReference.Path, fileReference.ContentId);
                        _fileReferenceService.DeleteAndSave(fileReference);
                    }
                }
            }
            catch (Exception ex)
            {
                failedUploads.Add($"{fileReference.Path} (error: {ex.Message})");
            }
        }

        _logger.LogDebug("finished upload-all-to-s3");
        return Ok(new
        {
            UploadedFiles = uploadedFiles,
            FailedUploads = failedUploads,
        });
    }


    /// <summary>
    /// Get the running time of the file and update FileReference
    /// </summary>
    /// <param name="publishedAfter">Optional, only process content files published after the specified date</param>
    /// <param name="publishedBefore">Optional, only process content files published before the specified date</param>
    /// <param name="limit">Optional, limit the number of files processed</param>
    /// <param name="force">Optional, whether to force processing of files</param>
    /// <returns>Processing result, including lists of successful and failed files</returns>
    [HttpPost("update-media-duration")]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(typeof(Dictionary<string, List<string>>), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ErrorResponseModel), (int)HttpStatusCode.BadRequest)]
    [SwaggerOperation(Tags = new[] { "Storage" })]
    public async Task<IActionResult> UpdateMediaDurationAsync(
        [FromQuery] DateTime? publishedAfter = null,
        [FromQuery] DateTime? publishedBefore = null,
        [FromQuery] int? limit = null,
        [FromQuery] bool force = false)
    {
        _logger.LogInformation("Starting to update media file duration");

        var fileReferences = await _fileReferenceService.GetFiles(publishedAfter, publishedBefore, limit ?? -1, force);
        _logger.LogInformation("Retrieved {Count} file references for processing", fileReferences.Count());

        // Only process audio and video files
        fileReferences = fileReferences.Where(fr =>
            fr.ContentType.StartsWith("video/", StringComparison.OrdinalIgnoreCase) ||
            fr.ContentType.StartsWith("audio/", StringComparison.OrdinalIgnoreCase));
        _logger.LogInformation("Filtered to {Count} audio/video file references", fileReferences.Count());

        var successFiles = new List<string>();
        var failedFiles = new List<string>();

        foreach (var fileReference in fileReferences)
        {
            try
            {
                _logger.LogInformation("Processing file: {Path}", fileReference.Path);
                if (fileReference.RunningTime > 0 && !force)
                {
                    _logger.LogInformation("Skipping file {Path} as it already has a running time of {RunningTime}ms, contentId: {ContentId}", fileReference.Path, fileReference.RunningTime, fileReference.ContentId);
                    continue; // Skip files that already have running time unless forced
                }

                // Check S3 path first
                if (!string.IsNullOrEmpty(fileReference.S3Path))
                {
                    _logger.LogInformation("Checking S3 path: {S3Path}", fileReference.S3Path);
                    var duration = await _s3StorageService.GetMediaDurationAsync(fileReference.S3Path);
                    if (duration.HasValue)
                    {
                        fileReference.RunningTime = (int)Math.Round(duration.Value * 1000); // convert to milliseconds
                        await _fileReferenceService.UpdateAsync(fileReference);
                        successFiles.Add($"{fileReference.Path} (S3 duration: {fileReference.RunningTime}ms)");
                        _logger.LogDebug("Updated S3 duration for file {Path} contentId: {ContentId} duration: {Duration}ms", fileReference.Path, fileReference.ContentId, fileReference.RunningTime);
                        continue; // Skip to the next file after processing S3
                    }
                }
                else
                {
                    // If it is local file
                    var filePath = Path.Combine(_storageOptions.GetUploadPath(), fileReference.Path);

                    try
                    {
                        _logger.LogInformation("File found, getting duration for {Path}", filePath);
                        var duration = await FfmpegHelper.GetVideoDurationAsync(filePath);
                        fileReference.RunningTime = (int)Math.Round(duration * 1000);
                        await _fileReferenceService.UpdateAsync(fileReference);
                        successFiles.Add($"{fileReference.Path} (duration: {fileReference.RunningTime}ms)");
                        _logger.LogDebug("Updated duration for file {Path} contentId: {ContentId} duration: {Duration}ms", fileReference.Path, fileReference.ContentId, fileReference.RunningTime);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error parsing duration for file: {Path}, contentId: {ContentId}", fileReference.Path, fileReference.ContentId);
                        failedFiles.Add($"{fileReference.Path} (unable to parse duration: {ex.Message})");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing file: {Path}, contentId: {ContentId}", fileReference.Path, fileReference.ContentId);
                failedFiles.Add($"{fileReference.Path} (error: {ex.Message})");
            }
        }

        _logger.LogInformation("Media file duration update completed with {SuccessCount} successes and {FailedCount} failures", successFiles.Count, failedFiles.Count);
        return Ok(new
        {
            SuccessFiles = successFiles,
            FailedFiles = failedFiles
        });
    }
    #endregion
}
